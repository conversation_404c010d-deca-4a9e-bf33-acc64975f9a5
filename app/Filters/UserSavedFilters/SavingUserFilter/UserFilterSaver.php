<?php

namespace App\Filters\UserSavedFilters\SavingUserFilter;

use App\Filters\UserSavedFilters\SavingUserFilter\Dto\ResponseStatus;
use App\Repositories\SavedFilterRepository;
use App\Repositories\UserRepository;

class UserFilterSaver
{
    public function __construct(private UserRepository $userRepository, private SavedFilterRepository $savedFilterRepository)
    {
    }

    /**
     * @param array<string, mixed> $paramsToSave Key is param name
     */
    public function saveFilterForUser(int $userId, string $filterName, array $paramsToSave): ResponseStatus
    {
        $user = $this->userRepository->findById($userId);

        if ($user === null) {
            return ResponseStatus::USER_NOT_FOUND;
        }

        if ($user->plan === null) {
            return ResponseStatus::USER_WITHOUT_SUBSCRIPTION;
        }

        if ($this->userExceedsFilterLimit($userId)) {
            return ResponseStatus::LIMIT_EXCEEDED;
        }

        if ($this->savedFilterRepository->filterNameExistsForUser($userId, $filterName)) {
            return ResponseStatus::DUPLICATED_NAME;
        }

        $this->savedFilterRepository->save($userId, $filterName, $paramsToSave);

        return ResponseStatus::SUCCESS;
    }

    private function userExceedsFilterLimit(int $userId): bool
    {
        $filtersLimitInPlan = $this->userRepository->getFiltersLimitForUserPlan($userId);
        $savedFiltersCount = $this->userRepository->getSavedFiltersCountForUser($userId);

        return $savedFiltersCount >= $filtersLimitInPlan;
    }
}
