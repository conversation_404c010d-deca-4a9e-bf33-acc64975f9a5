<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use <PERSON><PERSON>\Cashier\Billable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use App\Models\Subscription;

class User extends Authenticatable
{
    use HasRoles;
    use Billable;
    use HasFactory;
    use Notifiable;
    use SoftDeletes;
    use \App\Traits\MustVerifyEmail;
    use \App\Traits\CanResetPassword;

    protected $appends = ['active', 'emailStatus'];

    protected $fillable = [
        'nickname',
        'email',
        'phone',
        'phone_prefix',
        'password',
        'consent_marketing_at',
        'consent_marketing',
        'consent_public_ticket_at',
        'consent_public_ticket',
        'consent_terms_at',
        'consent_terms',
        'remember_token',
        'phone_verified_at',
        'email_verified_at',
        'required_verification',
        'plan_id'
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'phone_verified_at' => 'datetime',
            'password' => 'hashed',
            'consent_marketing_at' => 'datetime',
            'consent_terms_at' => 'datetime',
            'consent_public_ticket_at' => 'datetime',
            'required_verification' => 'boolean',
        ];
    }

    public function reminders()
    {
        return $this->hasMany(Reminder::class);
    }

    public function getPhoneProviderAttribute()
    {
        if( $this->phone === null ) {
            return null;
        }

        $countries = config('countries');

        $country = null;

        foreach ($countries as $prefix => $data) {
            if( $this->phone_prefix === $data['phone_code'] ){
                $country = $data;
                break;
            }
        }

        return $country['phone_provider'] ?? null;
    }

    public function getFullPhoneAttribute()
    {
        return $this->phone_prefix . ' ' . $this->phone;
    }

    public function hasVerifiedPhone()
    {
        return $this->phone_verified_at !== null;
    }

    public function hasPhoneProvider()
    {
        return $this->phone_provider !== null;
    }

    public function plan()
    {
        return $this->belongsTo(Plan::class);
    }

    public function devices()
    {
        return $this->hasMany(Device::class);
    }

    public function trustedDevices()
    {
        return $this->devices()->where('trusted', true);
    }

    public function untrustedDevices()
    {
        return $this->devices()->where('trusted', false);
    }

    public function hasTrustedDeviceOfType(string $type)
    {
        return $this->trustedDevices()->where('type', $type)->exists();
    }

    public function getConsentMarketingAttribute()
    {
        return $this->consent_marketing_at !== null;
    }

    public function setConsentMarketingAttribute($value)
    {
        $this->consent_marketing_at = $value ? now() : null;
    }

    public function getConsentPublicTicketAttribute()
    {
        return $this->consent_public_ticket_at !== null;
    }

    public function setConsentPublicTicketAttribute($value)
    {
        $this->consent_public_ticket_at = $value ? now() : null;
    }

    public function getConsentTermsAttribute()
    {
        return $this->consent_terms_at !== null;
    }

    public function setConsentTermsAttribute($value)
    {
        $this->consent_terms_at = $value ? now() : null;
    }

    public function hasAcceptConsentTerms()
    {
        return $this->consent_terms_at !== null;
    }

    public function sessions()
    {
        return $this->hasMany(Session::class);
    }

    public function lastActivity()
    {
        return optional($this->sessions->first())->getLastActiveAttribute();
    }

    public function getActiveAttribute()
    {
        return is_null($this->deleted_at) ? '1' : '0';
    }

    public function getEmailStatusAttribute()
    {
        return is_null($this->email_verified_at) ? '0' : '1';
    }

}
