<?php

namespace App\Livewire\Profile;

use App\Actions\Fortify\PasswordValidationRules;
use App\Services\UserService;
use Livewire\Attributes\Validate;
use Livewire\Component;

class UpdateUserPasswordForm extends Component
{
    use PasswordValidationRules;

    public string $current_password = '';
    public string $password = '';
    public string $password_confirmation = '';
    
    protected UserService $userService;

    public function __construct()
    {
        $this->userService = app()->make(UserService::class);
    }

    protected function rules()
    {
        return [
            'current_password' => ['required', 'string', $this->passwordRules(), 'current_password:web'],
            'password' => ['required', 'string', $this->passwordRules(), 'confirmed'],
        ];
    }

    public function render()
    {
        return view('profile.livewire.update-user-password-form');
    }

    public function update()
    {
        $this->validate();

        $email = auth()->user()->email;

        $this->userService->forceUpdateUserData([
            'password' => bcrypt($this->password),
        ]);

        $this->reset();

        redirect()->route('login')->with('email', $email);

        return true;
    }

}
