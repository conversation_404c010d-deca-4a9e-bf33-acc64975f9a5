<?php

namespace App\Repositories;

use App\Filters\UserSavedFilters\Common\Dto\SavedFilterDto;
use App\Models\SavedFilter;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class UserRepository
{
    public function all()
    {
        return User::all();
    }

    public function findById(int $id): ?User
    {
        return User::find($id);
    }

    public function findByEmail(string $email)
    {
        return User::where('email', $email)->first();
    }

    public function create(array $data)
    {
        return User::create($data);
    }

    public function update(User $user, array $data)
    {
        return $user->update($data);
    }

    public function forceUpdate(User $user, array $data)
    {
        return $user->forceFill($data)->save();
    }

    public function delete(User $user)
    {
        return $user->delete();
    }

    public function deleteById(int $id)
    {
        return User::destroy($id);
    }

    public function deleteByEmail(string $email)
    {
        return User::where('email', $email)->delete();
    }

    public function firstOrCreate(array $attributes, array $values = [])
    {
        return User::firstOrCreate($attributes, $values);
    }

    /**
     * @param array<string, array<int|string>|int|string|null> $filters
     */
    public function allWithSortingAdvancedFiltersPagination(int $pagination, string $column, bool $descending = true, array $filters = []): LengthAwarePaginator
    {
        return $this->getQueryForUsersWithSortingAndFilters($column, $descending, $filters)
            ->paginate($pagination);
    }

    /**
     * @param array<string, array<int|string>|int|string|null> $filters
     */
    public function getQueryForUsersWithSortingAndFilters(string $column, bool $descending = true, array $filters = []): Builder
    {
        $query = User::withoutGlobalScope(SoftDeletingScope::class)->with(['sessions' => function($q){
            return $q->orderBy('id', 'desc')->limit(1);
        }]);

        $query->with('subscriptions');

        $query->orderBy($column, $descending ? 'desc' : 'asc');

        foreach ($filters as $column => $value) {
            if ($value !== '' && strlen($value) > 3) {
                if ($column == 'filter') {
                    $query->where('email', 'like', "%{$value}%")
                        ->orWhere('nickname', 'like', "%{$value}%")
                        ->orWhere('phone', 'like', "%{$value}%");
                }
            }
        }

        return $query;
    }

    public function getFiltersLimitForUserPlan(int $userId): int
    {
        return User::where('id', $userId)->with(['plan' => function($q){
            return $q->select('id', 'filter_limit');
        }])->first()->plan->filter_limit;
    }

    public function getSavedFiltersCountForUser(int $userId): int
    {
        return SavedFilter::where('user_id', $userId)->count();
    }

    /**
     * @return iterable<SavedFilter>
     */
    public function getSavedFilters(int $userId): iterable
    {
        return SavedFilter::where('user_id', $userId)->get();
    }
}
