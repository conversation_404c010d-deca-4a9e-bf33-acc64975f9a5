<?php

namespace App\Repositories;

use App\Models\SavedFilter;

class SavedFilterRepository
{
    public function save(int $userId, string $name, array $params): int
    {
        return SavedFilter::create([
            'user_id' => $userId,
            'name' => $name,
            'params' => $params,
        ])->id;
    }

    public function filterNameExistsForUser(int $userId, string $name): bool
    {
        return SavedFilter::where('user_id', $userId)->where('name', $name)->exists();
    }
}
