<?php

namespace App\Services;

use App\Models\PredictionApi\Country;
use App\Repositories\CountryRepository;

class CountryService
{
    protected CountryRepository $countryRepository;

    public function __construct(CountryRepository $countryRepository)
    {
        $this->countryRepository = $countryRepository;
    }

    public function getCountryWithLeaguesAndFixtures(Country $country)
    {
        return $this->countryRepository->getCountryWithLeaguesAndFixtures($country);
    }


}