@php
$sumProgress = function($fixtures_count, $upcoming_fixtures_count) {
    if ($fixtures_count == 0) {
        return 0;
    }

    $passed_fixtures_count = $fixtures_count - $upcoming_fixtures_count;

    return (int) ($passed_fixtures_count / $fixtures_count * 100);
};
@endphp
<div class="col-span-8">
    <x-admin.app-input :name="__('filter.search')" alt-name="Lig po kraju i nazwie" placeholder="wpisz min 3 znaki..." id="filtersAccordion"  wire:ignore.self aria-labelledby="simpleFilters" wire:model.live.debounce.500ms="filter" />
    <div class="accordion relative flex flex-col w-full h-full overflow-hidden text-gray-700 bg-white shadow-md rounded-lg bg-clip-border mb-3" id="filtersAccordion">
        <table class="w-full text-left table-auto min-w-max overflow-hidden">
            <thead>
                <tr>
                    <x-table-header-sort wire:click="sort('id')" :isSorting="$sortColumn == 'sort'"  class="p-4 border-b border-slate-300 bg-slate-50 font-bold"
                        :sortDescending="$sortDescending"><p class="block text-sm font-normal leading-none text-slate-500">#</p></x-table-header-sort>
                    <x-table-header-sort wire:click="sort('country')" :isSorting="$sortColumn == 'country'" class="p-4 border-b border-slate-300 bg-slate-50 font-bold"
                        :sortDescending="$sortDescending"><p class="block text-sm font-normal leading-none text-slate-500">@lang('league.country')</p></x-table-header-sort>
                    <x-table-header-sort wire:click="sort('name')" :isSorting="$sortColumn == 'name'" class="p-4 border-b border-slate-300 bg-slate-50 font-bold"
                                         :sortDescending="$sortDescending"><p class="block text-sm font-normal leading-none text-slate-500">@lang('league.singular')</p></x-table-header-sort>
                    <th class="p-4 border-b border-slate-300 bg-slate-50 font-bold relative p-2"><p class="block text-sm font-normal leading-none text-slate-500">@lang('common.visibility')</p></th>
                    <x-table-header-sort
                        wire:click="sort('name')"
                        :isSorting="$sortColumn == 'name'"
                        class="p-4 border-b border-slate-300 bg-slate-50 font-bold"
                        :sortDescending="$sortDescending">
                        <p class="block text-sm font-normal leading-none text-slate-500">@lang('league.season')</p>
                    </x-table-header-sort>
                    <th class="p-4 border-b border-slate-300 bg-slate-50 font-bold relative p-2">
                        <p class="block text-xs font-normal leading-none text-slate-500">@lang('league.season-start-date')</p>
                        <p class="block text-sm font-normal leading-none text-slate-500">@lang('league.season-end-date')</p>
                    </th>
                    <th class="p-4 border-b border-slate-300 bg-slate-50 font-bold relative p-2">
                        <p class="block text-sm font-normal leading-none text-slate-500">@lang('league.fixtures-status')</p>
                    </th>
                    <th class="p-4 border-b border-slate-300 bg-slate-50 font-bold relative p-2">
                        <p class="block text-sm font-normal leading-none text-slate-500">@lang('league.next-fixture')</p>
                        <p class="block text-sm font-normal leading-none text-slate-500">@lang('league.last-fixture')</p>
                    </th>
                </tr>
            </thead>
            <tbody wire:sortable="updateOrder">
                @foreach ($leagues as $index => $league)
                    @can('league_edit')
                        <tr wire:sortable.item="{{ $index }}" class="draggable even:bg-gray-50 hover:bg-slate-200">
                    @else
                        <tr class="even:bg-gray-50 hover:bg-slate-200">
                    @endcan
                        <td class="px-2 border-b border-slate-200 py-2">{{ $league->sort }}</td>
                        <td class="px-2 border-b border-slate-200 py-2">
                            <p class="text-xs text-slate-500"><img src="{{ $league->country->cachedFlag }}" class="h-5" loading="lazy" alt="{{ $league->country->name }}">{{ $league->country->name }}</p>
                        </td>
                        <td class="px-2 border-b border-slate-200 py-2">
                            <img src="{{ $league->cachedLogo }}" class="h-5" loading="lazy" alt="{{ $league->name }}"><p class="text-xs text-slate-500">{{ $league->name }}</p>
                        </td>
                        @can('league_edit')
                            <td class="px-2 border-b border-slate-200 py-2">
                                <x-wireui-toggle wire:model.live="leaguesArr.{{ $index }}.visibility" positive xl/>
                            </td>
                        @endcan
                            <td class="px-2 border-b border-slate-200 py-2"><p class="text-xs text-slate-500">{{ $league->season->year }}</p></td>
                        <td class="px-2 border-b border-slate-200 py-2">
                            <p class="text-xs text-slate-500">{{ $league->season->start }}</p>
                            <p class="text-xs text-slate-500">{{ $league->season->end }}</p>
                        </td>
                        <td class="px-2 border-b border-slate-200 py-2">
                            <x-progress-bar :progress="$sumProgress($league->totalFixtures ?? 0, $league->nextFixtures ?? 0)" />
                            <p class="block text-xs font-normal leading-none text-slate-500">
                                Total:{{ $league->totalFixtures ?? 0 }}
                                NS:{{ $league->nextFixtures ?? 0 }}
                                FT:{{ ($league->totalFixtures ?? 0 - $league->nextFixtures ?? 0)}}
                            </p>
                        </td>
                        <td class="px-2 border-b border-slate-200 py-2">
                            <p class="text-xs text-slate-500">
                            @if($league->nextFixtureId->first())
                                <a href="/{{ 'fixture/' . $league->nextFixtureId->first()->id }}">
                                {{ $league->nextFixtureId->first()->date }}
                                    </a>
                            @else
                                N/A
                            @endif
                            </p>
                            <p class="text-xs text-slate-500">
                            @if($league->lastFixtureId->first())
                                <a href="/{{ 'fixture/' . $league->lastFixtureId->first()->id }}">
                                {{ $league->lastFixtureId->first()->date }}
                                    </a>
                            @else
                                N/A
                            @endif
                            </p>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    {{ $leagues->links() }}
</div>

@push('styles')
    <style>
        td,
        th {
            padding: 4px;
        }

        .btn.focus,
        .btn:focus {
            box-shadow: none;
        }

        .accordion>.card {
            overflow: unset;
        }
        .draggable-mirror{
            width: 80%;
            display: table;
            background: #F3F4F6;
            padding: 10px;
            margin: 10px 0 100px;
        }
    </style>
@endpush
