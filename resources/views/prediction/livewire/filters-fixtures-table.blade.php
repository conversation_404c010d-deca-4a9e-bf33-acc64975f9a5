@php
    $columns = [
        ['key' => 'id', 'label' => 'Match ID', 'className' => 'w-[330px]'],
        ['key' => 'league', 'label' => __('league.country-singular'), 'className' => 'w-[330px]'],
        ['key' => 'date', 'label' => __('fixture.hour-short'), 'tooltip' =>  __('fixture.hour'),  'className' => 'w-[50px]'],
        ['key' => 'home_team_id', 'label' => __('club.home'), 'className' => 'w-[380px]'],
        ['key' => 'aw', 'label' => __('league.away-win-short'), 'tooltip' => __('league.away-win'), 'className' => ''],
        ['key' => 'mr', 'label' => __('fixture.result-short'), 'tooltip' => __('fixture.result'), 'className' => ''],
        ['key' => 'hw', 'label' => __('league.home-win-short'), 'tooltip' => __('league.home-win'), 'className' => ''],
        ['key' => 'draw', 'label' => __('league.draw-short'), 'tooltip' => __('league.draw'), 'className' => ''],
        ['key' => 'away_team_id', 'label' => __('club.away'), 'className' => 'w-[380px]'],
        ['key' => 'show', 'label' => '', 'className' => ''],
    ];
@endphp

<div class="grid grid-cols-3 gap-3">
    <div class="col-span-1 lg:col-span-1">
        <x-tile class="py-3 px-3">
            <h2><strong>Filtry</strong></h2>
                <div class="py-1 divide-y">
                    @livewire('filters.goals-scored-filter')
                </div>
                <div class="py-1 divide-y">
                    @livewire('filters.match-status-filter')
                </div>
                <div class="py-1 divide-y">
                    @livewire('filters.corners-filter')
                </div>
        </x-tile>
    </div>
    <div class="col-span-2 lg:col-span-2">
        <x-tile>
            @if (!empty($filterParamsJson))
                <div class="save-filter">
                    <button id="save-filter-button" data-params="{{ $filterParamsJson }}" class="w-full bg-blue-500 text-white px-4 py-2 rounded mb-4">Zapisz parametry filtra</button>
                </div>
            @endif
            <div class="overflow-x-auto relative">
                <div wire:loading wire:target="changeDay" class="w-full absolute backdrop-blur-sm top-0 inset-0 z-50">
                    <div class="w-full flex justify-center py-24">
                        <x-loading-spinner />
                    </div>
                </div>
                <x-resource-table class="min-w-[650px]" :columns="$columns" :resources="[
                    'data' => $fixtures ?? [],
                ]">
                    @foreach($fixtures ?? [] as $fixture)
                        <tr class="odd:bg-white even:bg-slate-50 h-8 text-xs">
                            <td class="px-2 border-l">{{ $fixture->id }}</td>
                            <td class="px-2">
                                <a href="{{route('league.show', [ 'league' => $fixture->league->slug ?? 'error', 'country' => $fixture->league->country->slug ?? 'error' ])}}" class="hover:text-blue-700 hover:underline flex gap-2">
                                    <div class="flex gap-2">
                                        <div class="w-8 flex items-center justify-center">
                                            @if($fixture->league->country->cachedFlag)
                                                <img
                                                    src="{{ $fixture->league->country->cachedFlag }}"
                                                    class="h-5"
                                                    loading="lazy"
                                                    alt="{{ $fixture->league->country->name }}"
                                                    x-tooltip.raw="{{ $fixture->league->country->name }}"
                                                >
                                            @endif
                                        </div>
                                        <div class="w-8 flex items-center justify-center">
                                            <img src="{{ $fixture->league->cachedLogo }}" class="max-w-5 max-h-5" loading="lazy" alt="{{ $fixture->league->name }}">
                                        </div>
                                        {{ $fixture->league->name }}
                                        <p class="size-3 pt-1">
                                            <x-icon.arrow-top-right-on-square />
                                        </p>
                                    </div>
                                </a>
                            </td>
                            <td class="px-2 border-l">{{ convertDateToLocal($fixture->date, 'Y-m-d H:i') }}</td>
                            <td class="px-2 border-l">
                                @isset($fixture->homeClub)
                                    <a href="{{route('club.show', [ 'club' => $fixture->homeClub->slug ?? 'error' ])}}" class="hover:text-blue-700 hover:underline flex gap-2">
                                        <div class="w-6 flex items-center justify-center">
                                            <img src="{{ $fixture->homeClub->cachedLogo }}" class="h-5" loading="lazy" alt="{{ $fixture->homeClub->name }}">
                                        </div>
                                        {{ $fixture->homeClub->name }}
                                        <p class="size-3 pt-1">
                                            <x-icon.arrow-top-right-on-square />
                                        </p>
                                    </a>
                                @else
                                    <span class="text-red-500">{{ __('club.unknown') }} <span class="text-xs text-gray-400">[ID: {{ $fixture->home_team_id }}]</span></span>
                                @endisset
                            </td>
                            <td class="px-2 py-1 border-l">
                                <div class="flex justify-center w-full">
                                    {{ $fixture->goals_home ?? '' }} - {{ $fixture->goals_away ?? '' }}
                                </div>
                            </td>
                            <td class="px-2 py-1 border-l">
                                <div class="flex justify-center w-full">
                                    @if( isset($fixture->predictions->pre_percent_home) )
                                        <x-club.win-trophy :percent="$fixture->predictions->pre_percent_home" />
                                    @else
                                        <span class="text-gray-400 text-xs">N/A</span>
                                    @endif
                                </div>
                            </td>
                            <td class="px-2 border-l">
                                <div class="flex justify-center w-full">
                                    @if( isset($fixture->predictions->pre_percent_draw) )
                                        <x-club.draw-shield :percent="$fixture->predictions->pre_percent_draw" />
                                    @else
                                        <span class="text-gray-400 text-xs">N/A</span>
                                    @endif
                                </div>
                            </td>
                            <td class="px-2 border-l">
                                <div class="flex justify-center w-full">
                                    @if( isset($fixture->predictions->pre_percent_away) )
                                        <x-club.win-trophy :percent="$fixture->predictions->pre_percent_away" />
                                    @else
                                        <span class="text-gray-400 text-xs">N/A</span>
                                    @endif
                                </div>
                            </td>
                            <td class="px-2 border-l">
                                @isset($fixture->awayClub)
                                    <a href="{{route('club.show', [ 'club' => $fixture->awayClub->slug ?? 'error' ])}}" class="hover:text-blue-700 hover:underline flex gap-2">
                                        <div class="w-6 flex items-center justify-center">
                                            <img src="{{ $fixture->awayClub->cachedLogo }}" class="h-5" loading="lazy" alt="{{ $fixture->awayClub->name }}">
                                        </div>
                                        {{ $fixture->awayClub->name }}
                                        <p class="size-3 pt-1">
                                            <x-icon.arrow-top-right-on-square />
                                        </p>
                                    </a>
                                @else
                                    <span class="text-red-500">{{ __('club.unknown') }} <span class="text-xs text-gray-400">[ID: {{ $fixture->away_team_id }}]</span></span>
                                @endisset
                            </td>
                            <td class="px-2 ">
                                <a href="{{ route('fixture.show', [ 'fixtureSlug' => $fixture->generateSlug($fixture->homeClub, $fixture->awayClub) ]) }}" class="text-blue-500 hover:text-blue-400 hover:underline flex gap-1 items-center text-sm" title="{{__('main.action.show-more')}}">
                                    <p class="size-3">
                                        <x-icon.chevron-double-right />
                                    </p>
                                </a>
                            </td>
                        </tr>
                    @endforeach
                </x-resource-table>
            </div>
            @if( $fixturesCount > $limit )
                <div class="pl-3 py-2 text-center" x-data x-intersect.margin.300px:enter="$wire.call('loadMore')">
                    <x-loading-spinner />
                </div>
            @endif
            <div class="w-full flex justify-end text-sm text-gray-300">
                {{ __('league.loaded-fixtures', [ 'loaded' => $limit < $fixturesCount ? $limit : $fixturesCount, 'total' => $fixturesCount ]) }}
            </div>
        </x-tile>
    </div>

    <!-- Save Filter Modal -->
    <div id="save-filter-modal" class="hidden">
        <!-- Modal Backdrop -->
        <div id="modal-backdrop" class="fixed inset-0 overflow-y-auto px-4 py-6 sm:px-0 z-50">
            <div class="fixed inset-0 transform transition-all" onclick="closeModal()">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <!-- Modal Content -->
            <div class="mb-6 bg-white rounded-lg overflow-hidden shadow-xl transform transition-all sm:w-full sm:max-w-md sm:mx-auto">
                <!-- Modal Header -->
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Zapisz filtr</h3>
                </div>

                <!-- Modal Body -->
                <div class="px-6 py-4">
                    <!-- Success/Error Message -->
                    <div id="message-container" class="mb-4 p-3 rounded hidden">
                        <span id="message-text"></span>
                    </div>

                    <!-- Form -->
                    <div id="form-container">
                        <label for="filter-name" class="block text-sm font-medium text-gray-700 mb-2">Nazwa</label>
                        <input type="text"
                            id="filter-name"
                            class="w-full border-gray-300 focus:border-indigo-500 rounded-md shadow-sm"
                            placeholder="Wprowadź nazwę filtra">
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
                    <button type="button"
                        id="cancel-btn"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        onclick="closeModal()">
                        Anuluj
                    </button>
                    <button type="button"
                        id="save-btn"
                        class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                        onclick="saveFilter()">
                        <span id="save-btn-text">Zapisz</span>
                        <span id="save-btn-loading" class="flex items-center hidden">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Zapisywanie...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Pure JavaScript modal functions
        function openModal() {
            console.log('Opening modal');
            const modal = document.getElementById('save-filter-modal');
            modal.classList.remove('hidden');
            resetModal();

            // Focus on input field
            const input = document.getElementById('filter-name');
            setTimeout(() => input.focus(), 100);
        }

        function closeModal() {
            console.log('Closing modal');
            const modal = document.getElementById('save-filter-modal');
            modal.classList.add('hidden');
            resetModal();
        }

        function resetModal() {
            // Clear input
            document.getElementById('filter-name').value = '';

            // Hide message
            const messageContainer = document.getElementById('message-container');
            messageContainer.classList.add('hidden');

            // Show form
            document.getElementById('form-container').style.display = 'block';

            // Reset buttons
            document.getElementById('save-btn').disabled = false;
            document.getElementById('save-btn-text').classList.remove('hidden');
            document.getElementById('save-btn-loading').classList.add('hidden');
        }

        function showMessage(message, type) {
            const messageContainer = document.getElementById('message-container');
            const messageText = document.getElementById('message-text');

            messageText.textContent = message;

            // Remove existing classes
            messageContainer.classList.remove('bg-green-100', 'text-green-700', 'bg-red-100', 'text-red-700');

            // Add appropriate classes
            if (type === 'success') {
                messageContainer.classList.add('bg-green-100', 'text-green-700');
            } else {
                messageContainer.classList.add('bg-red-100', 'text-red-700');
            }

            messageContainer.classList.remove('hidden');

            // Hide form when showing message
            if (type === 'success') {
                document.getElementById('form-container').style.display = 'none';
            }
        }

        function setLoading(loading) {
            const saveBtn = document.getElementById('save-btn');
            const saveBtnText = document.getElementById('save-btn-text');
            const saveBtnLoading = document.getElementById('save-btn-loading');

            saveBtn.disabled = loading;

            if (loading) {
                saveBtnText.classList.add('hidden');
                saveBtnLoading.classList.remove('hidden');
            } else {
                saveBtnText.classList.remove('hidden');
                saveBtnLoading.classList.add('hidden');
            }
        }

        function saveFilter() {
            const filterNameInput = document.getElementById('filter-name');
            const filterName = filterNameInput.value.trim();

            if (!filterName) {
                return;
            }

            const saveFilterButton = document.getElementById('save-filter-button');
            if (!saveFilterButton) {
                console.error('Save filter button not found');
                return;
            }

            const params = saveFilterButton.getAttribute('data-params');

            setLoading(true);

            // Get CSRF token
            const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            // Prepare request data
            const requestData = {
                name: filterName,
                params: JSON.parse(params)
            };

            // Make AJAX request
            fetch('/saved-filters', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': token,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                setLoading(false);

                if (data.status === 'Success') {
                    showMessage('Filtr zapisano pomyślnie', 'success');

                    // Auto-close modal after 2 seconds
                    setTimeout(() => {
                        closeModal();
                    }, 2000);
                } else if (data.status === 'Failed') {
                    showMessage(data.message || 'Wystąpił błąd podczas zapisywania filtra', 'error');
                } else {
                    showMessage('Wystąpił nieoczekiwany błąd', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                setLoading(false);
                showMessage('Wystąpił błąd podczas komunikacji z serwerem', 'error');
            });
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            const saveFilterButton = document.getElementById('save-filter-button');
            console.log('Save filter button found:', saveFilterButton);

            if (saveFilterButton) {
                saveFilterButton.addEventListener('click', function() {
                    console.log('Button clicked, opening modal');
                    openModal();
                });
            } else {
                console.log('Save filter button not found');
            }

            // Add Enter key support for input
            const filterNameInput = document.getElementById('filter-name');
            if (filterNameInput) {
                filterNameInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        saveFilter();
                    }
                });
            }

            // Add Escape key support to close modal
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const modal = document.getElementById('save-filter-modal');
                    if (!modal.classList.contains('hidden')) {
                        closeModal();
                    }
                }
            });
        });
    </script>
    </div>
</div>

