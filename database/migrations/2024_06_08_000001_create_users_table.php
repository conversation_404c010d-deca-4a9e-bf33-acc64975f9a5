<?php

use App\Models\Plan;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();

            $table->string('email');
            $table->timestamp('email_verified_at')->nullable();
            $table->string('phone_prefix')->nullable();
            $table->string('phone')->nullable();
            $table->timestamp('phone_verified_at')->nullable();
            $table->string('password')->nullable();
            $table->boolean('required_verification')->default(true);

            $table->string('nickname')->nullable();

            $table->timestamp('consent_marketing_at')->nullable();
            $table->timestamp('consent_public_ticket_at')->nullable();
            $table->timestamp('consent_terms_at')->nullable();

            $table->foreignIdFor(Plan::class)->nullable();

            $table->rememberToken();

            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('devices', function (Blueprint $table) {
            $table->id();

            $table->foreignIdFor(User::class)->constrained()->index();
            $table->string('type');
            $table->string('ip');
            $table->string('user_agent');

            $table->boolean('trusted')->default(false);

            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('devices');
        Schema::dropIfExists('reminders');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
        Schema::dropIfExists('users');
    }
};
