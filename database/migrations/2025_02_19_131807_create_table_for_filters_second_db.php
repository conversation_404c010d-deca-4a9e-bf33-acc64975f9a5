<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'readonly_pgsql';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $sql = <<<SQL
        create table if not exists public.temp_table_current_weeks
        (
            id                         integer      not null
                constraint temp_table_current_weeks_id_unique
                    unique,
            league_id                  integer      not null,
            season_id                  integer      not null,
            round_id                   integer      not null,
            home_team_id               integer      not null,
            away_team_id               integer      not null,
            home_rank                  smallint,
            away_rank                  smallint,
            timestamp                  timestamp(0) not null,
            datindex                   date         not null,
            "HomeGoalsForAVG"          jsonb,
            "HomeGoalsAgainstAVG"      jsonb,
            "HomeGoalsFor"             jsonb,
            "HomeGoalsAgainst"         jsonb,
            "HomeGoalsForMin"          jsonb,
            "HomeGoalsAgainstMin"      jsonb,
            "HomeGoalsDiff"            jsonb,
            "HomeCornerKicks"       jsonb,
            "HomeCornerKicksAVG"       jsonb,
            "HomeCornerKicksMin"       jsonb,
            "HomeYellowCardsAVG"       double precision,
            "HomeRedCardsAVG"          double precision,
            "HomeFoulsAVG"             double precision,
            "AwayGoalsForAVG"          jsonb,
            "AwayGoalsAgainstAVG"      jsonb,
            "AwayGoalsFor"             jsonb,
            "AwayGoalsAgainst"         jsonb,
            "AwayGoalsForMin"          jsonb,
            "AwayGoalsAgainstMin"      jsonb,
            "AwayGoalsDiff"            jsonb,
            "HomeMatchStatus"            jsonb,
            "AwayMatchStatus"            jsonb,
            "AwayCornerKicks"       jsonb,
            "AwayCornerKicksAVG"       jsonb,
            "AwayCornerKicksMin"       jsonb,
            "AwayYellowCardsAVG"       double precision,
            "AwayRedCardsAVG"          double precision,
            "AwayFoulsAVG"             double precision,
            "TotalHomeGoalsForAVG"     jsonb,
            "TotalHomeGoalsAgainstAVG" jsonb,
            "TotalHomeGoalsFor"        jsonb,
            "TotalHomeGoalsAgainst"    jsonb,
            "TotalHomeGoalsForMin"     jsonb,
            "TotalHomeGoalsAgainstMin" jsonb,
            "TotalHomeGoalsDiff"       jsonb,
            "TotalHomeCornerKicksAVG"  double precision,
            "TotalHomeYellowCardsAVG"  double precision,
            "TotalHomeRedCardsAVG"     double precision,
            "TotalHomeFoulsAVG"        double precision,
            "TotalAwayGoalsForAVG"     jsonb,
            "TotalAwayGoalsAgainstAVG" jsonb,
            "TotalAwayGoalsFor"        jsonb,
            "TotalAwayGoalsAgainst"    jsonb,
            "TotalAwayGoalsForMin"     jsonb,
            "TotalAwayGoalsAgainstMin" jsonb,
            "TotalAwayGoalsDiff"       jsonb,
            "TotalAwayCornerKicksAVG"  double precision,
            "TotalAwayYellowCardsAVG"  double precision,
            "TotalAwayRedCardsAVG"     double precision,
            "TotalAwayFoulsAVG"        double precision,
            "HomeWinPercent"           smallint     not null,
            "HomeDrawPercent"          smallint     not null,
            "HomeLostPercent"          smallint     not null,
            "AwayWinPercent"           smallint     not null,
            "AwayDrawPercent"          smallint     not null,
            "AwayLostPercent"          smallint     not null,
            "TotalHomeWinPercent"      smallint     not null,
            "TotalHomeDrawPercent"     smallint     not null,
            "TotalHomeLostPercent"     smallint     not null,
            "TotalAwayWinPercent"      smallint     not null,
            "TotalAwayDrawPercent"     smallint     not null,
            "TotalAwayLostPercent"     smallint     not null,
            as_home                    jsonb,
            as_away                    jsonb,
            as_total_home              jsonb,
            as_total_away              jsonb,
            created_at                 timestamp(0),
            updated_at                 timestamp(0)
        );

        SQL;

        DB::statement($sql);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('temp_table_current_weeks');
    }
};
