<?php

namespace Tests\Functional\Support;

use App\Models\Plan;

class PlanFixture
{
    public function create(
        ?string $name = null,
        ?int $filterLimit = null,
        ?int $leagueLimit = null,
        ?int $statsLimit = null,
        ?int $price = null,
    ): int {
        $name = $name ?? fake()->name();
        $filterLimit = $filterLimit ?? rand(1, 10);
        $leagueLimit = $leagueLimit ?? rand(1, 10);
        $statsLimit = $statsLimit ?? rand(1, 10);
        $price = $price ?? rand(1, 100);

        return Plan::create([
            'name' => $name,
            'filter_limit' => $filterLimit,
            'league_limit' => $leagueLimit,
            'stats_limit' => $statsLimit,
            'price' => $price,
        ])->id;
    }
}
