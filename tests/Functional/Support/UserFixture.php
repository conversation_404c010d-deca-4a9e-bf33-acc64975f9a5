<?php

namespace Tests\Functional\Support;

use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserFixture
{
    public function create(
        ?string $name = null,
        ?string $email = null,
        ?string $password = null,
        ?int $planId = null,
    ): int {
        $name = $name ?? fake()->name();
        $email = $email ?? fake()->unique()->safeEmail();
        $password = $password ?? 'password';

        return User::create([
            'name' => $name,
            'email' => $email,
            'password' => Hash::make($password),
            'plan_id' => $planId,
        ])->id;
    }
}
