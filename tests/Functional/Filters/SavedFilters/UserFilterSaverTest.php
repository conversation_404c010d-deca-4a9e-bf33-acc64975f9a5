<?php

namespace Tests\Functional\Filters\SavedFilters;

use App\Filters\UserSavedFilters\SavingUserFilter\Dto\ResponseStatus;
use App\Filters\UserSavedFilters\SavingUserFilter\UserFilterSaver;
use App\Repositories\UserRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\Functional\Support\PlanFixture;
use Tests\Functional\Support\UserFixture;
use Tests\TestCase;

class UserFilterSaverTest extends TestCase
{
    private UserFilterSaver $serviceUnderTest;
    private UserFixture $userFixture;
    private UserRepository $userRepository;

    protected function setUp(): void
    {
        $this->serviceUnderTest = app(UserFilterSaver::class);
        $this->userRepository = app(UserRepository::class);
        $this->userFixture = new UserFixture();
        $this->planFixture = new PlanFixture();

        parent::setUp();
    }

    public function test_saving_filter_for_non_existing_user(): void
    {
        /** @When */
        $actual = $this->serviceUnderTest->saveFilterForUser(123456789, 'test', []);

        /** @Then */
        self::assertEquals(ResponseStatus::USER_NOT_FOUND, $actual);
    }

    public function test_saving_filter_for_user_without_plan(): void
    {
        /** @Given */
        $userId = $this->userFixture->create();

        /** @When */
        $actual = $this->serviceUnderTest->saveFilterForUser($userId, 'test', []);

        /** @Then */
        self::assertEquals(ResponseStatus::USER_WITHOUT_SUBSCRIPTION, $actual);
    }

    public function test_saving_simple_filter(): void
    {
        /** @Given */
        $plan = $this->planFixture->create(filterLimit: 2);
        $userId = $this->userFixture->create(planId: $plan);

        /** @When */
        $actual = $this->serviceUnderTest->saveFilterForUser($userId, 'test', ['ab_param' => 'value']);

        /** @Then */
        self::assertEquals(ResponseStatus::SUCCESS, $actual);

        $userPlans = $this->userRepository->getSavedFilters($userId);
        self::assertCount(1, $userPlans);
        self::assertEquals('test', $userPlans[0]->name);
        self::assertEquals(['ab_param' => 'value'], $userPlans[0]->params);
    }

    public function test_user_exceeded_saved_filters_limit(): void
    {
        /** @Given */
        $plan = $this->planFixture->create(filterLimit: 2);
        $userId = $this->userFixture->create(planId: $plan);

        $status1 = $this->serviceUnderTest->saveFilterForUser($userId, 'test1', []);
        self::assertEquals(ResponseStatus::SUCCESS, $status1);
        $status2 = $this->serviceUnderTest->saveFilterForUser($userId, 'test2', []);
        self::assertEquals(ResponseStatus::SUCCESS, $status2);

        /** @When */
        $actual = $this->serviceUnderTest->saveFilterForUser($userId, 'test', []);

        /** @Then */
        self::assertEquals(ResponseStatus::LIMIT_EXCEEDED, $actual);
    }

    public function test_cannot_save_filter_with_duplicated_name(): void
    {
        /** @Given */
        $plan = $this->planFixture->create(filterLimit: 2);
        $userId = $this->userFixture->create(planId: $plan);

        $filterName = 'test';

        $status1 = $this->serviceUnderTest->saveFilterForUser($userId, $filterName, []);
        self::assertEquals(ResponseStatus::SUCCESS, $status1);

        /** @When */
        $actual = $this->serviceUnderTest->saveFilterForUser($userId, $filterName, []);

        /** @Then */
        self::assertEquals(ResponseStatus::DUPLICATED_NAME, $actual);
    }

    public function test_saving_complex_filter_params(): void
    {
        /** @Given */
        $plan = $this->planFixture->create(filterLimit: 2);
        $userId = $this->userFixture->create(planId: $plan);

        $params = [
            'param1' => true,
            'params2' => 12,
            'params4' => 12.42,
            'params5' => 'test',
            'params6' => 'ąćźół',
        ];

        /** @When */
        $actual = $this->serviceUnderTest->saveFilterForUser($userId, 'test', $params);

        /** @Then */
        self::assertEquals(ResponseStatus::SUCCESS, $actual);

        $userPlans = $this->userRepository->getSavedFilters($userId);
        self::assertCount(1, $userPlans);
        self::assertEquals('test', $userPlans[0]->name);
        self::assertEquals($params, $userPlans[0]->params);
    }
}
